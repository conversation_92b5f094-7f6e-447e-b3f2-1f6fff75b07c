/* Base Reset and Theme */
:root {
  --primary: #4CAF50;
  --primary-dark: #45a049;
  --accent: #667eea;
  --accent-dark: #764ba2;
  --text-dark: #2c3e50;
  --text-light: #ffffff;
  --bg-light: #f8f9fa;
  --bg-dark: #292929;
}

body {
  background: var(--bg-light);
  color: var(--text-dark);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2);
}

/* Buttons */
.btn {
  padding: 0.8rem 1.5rem;
  border-radius: 5px;
  font-weight: bold;
  font-size: 1rem;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--text-light);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-secondary:hover {
  background-color: var(--primary);
  color: var(--text-light);
}

/* Typography */
h1, h2, h3 {
  font-weight: bold;
  line-height: 1.3;
  color: var(--text-dark);
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.section-subtitle {
  color: var(--primary);
  font-size: 1.1rem;
  font-weight: 600;
}

/* Containers */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Navbar */
.navbar {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
}

.navbar ul {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 1rem 0;
  margin: 0;
}

.navbar li a {
  padding: 0.5rem 1rem;
  color: white;
  font-weight: 500;
  border-radius: 5px;
}

.navbar li a:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Forms */
form {
  margin: 0 auto;
  display: grid;
  width: 65%;
  gap: 1.5rem;
}

form input, form textarea, form select {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 2px solid #ccc;
  border-radius: 5px;
  transition: border-color 0.3s ease;
}

form input:focus, form textarea:focus, form select:focus {
  border-color: var(--primary);
  outline: none;
}

/* Utility */
.error_message {
  color: #ff4040;
  text-shadow: 0px 0px 10px #800;
}

span {
  background-color: #eaeaea;
  border-radius: 30px;
  box-shadow: 0px 0px 10px 1px rgba(0, 0, 0, 0.05);
  padding: 5px 10px;
  margin: 5px 0px;
}
.badge {
  background-color: #ec4138;
  color: #fff;
  font-size: 0.9rem;
  padding: 6px 12px;
  border-radius: 20px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.2);
}
