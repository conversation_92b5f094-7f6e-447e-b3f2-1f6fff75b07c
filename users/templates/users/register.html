{% extends 'main/base.html' %}

{% block content %}
<div class="register-container">
    <div class="register-header">
        <div class="logo">
            <div class="logo-icon">N</div>
            <span>NetFix</span>
        </div>
        <h1 class="register-title">Create Your Account</h1>
        <p class="register-subtitle">Join our community</p>
    </div>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
    {% endif %}

    <form class="register-form" method="post">
        {% csrf_token %}
        <input type="hidden" name="user_type" id="userType" value="customer">

        <!-- Simple Toggle Buttons -->
        <div class="user-type-toggle">
            <button type="button" class="toggle-btn active" onclick="showCustomer()">Customer</button>
            <button type="button" class="toggle-btn" onclick="showCompany()">Company</button>
        </div>

        <form class="register-form" id="registerForm" method="post">
            {% csrf_token %}
            <div class="success-message" id="successMessage">
                Registration successful! Please check your email to verify your account.
            </div>
            <div class="form-group">
                <label>Username</label>
                {{ customer_form.username }}
                {% if customer_form.username.errors %}
                    <div class="error-message">{{ customer_form.username.errors.0 }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label>Date of Birth</label>
                {{ customer_form.date_of_birth }}
                {% if customer_form.date_of_birth.errors %}
                    <div class="error-message">{{ customer_form.date_of_birth.errors.0 }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label>Password</label>
                {{ customer_form.password1 }}
                {% if customer_form.password1.errors %}
                    <div class="error-message">{{ customer_form.password1.errors.0 }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label>Confirm Password</label>
                {{ customer_form.password2 }}
                {% if customer_form.password2.errors %}
                    <div class="error-message">{{ customer_form.password2.errors.0 }}</div>
                {% endif %}
            </div>
        </div>

        <!-- Company Fields -->
        <div id="companyFields" class="form-fields" style="display: none;">
            <div class="form-group">
                <label>Email</label>
                {{ company_form.email }}
                {% if company_form.email.errors %}
                    <div class="error-message">{{ company_form.email.errors.0 }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label>Company Name</label>
                {{ company_form.username }}
                {% if company_form.username.errors %}
                    <div class="error-message">{{ company_form.username.errors.0 }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label>Field of Work</label>
                {{ company_form.field }}
                {% if company_form.field.errors %}
                    <div class="error-message">{{ company_form.field.errors.0 }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label>Password</label>
                {{ company_form.password1 }}
                {% if company_form.password1.errors %}
                    <div class="error-message">{{ company_form.password1.errors.0 }}</div>
                {% endif %}
            </div>
            <div class="form-group">
                <label>Confirm Password</label>
                {{ company_form.password2 }}
                {% if company_form.password2.errors %}
                    <div class="error-message">{{ company_form.password2.errors.0 }}</div>
                {% endif %}
            </div>
        </div>

            <button type="submit" class="submit-btn" id="submitBtn">Create Account</button>

            <div class="login-link">
                Already have an account? <a href="/login">Sign in here</a>
            </div>
        </form>
    </div>
</div>
    <script>
        // Simulated database for demo purposes
        const existingUsers = {
            emails: ['<EMAIL>', '<EMAIL>'],
            usernames: ['john_doe', 'acme_corp']
        };

        let currentUserType = 'customer';

        // Initialize form on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Disable company form fields initially since customer is active
            const companyForm = document.getElementById('companyForm');
            companyForm.querySelectorAll('input, select').forEach(input => {
                input.disabled = true;
                input.removeAttribute('required');
            });
            
            // Ensure customer form fields are enabled
            const customerForm = document.getElementById('customerForm');
            customerForm.querySelectorAll('input, select').forEach(input => {
                input.disabled = false;
                input.setAttribute('required', 'required');
            });
        });

        // Toggle between customer and company forms
        document.querySelectorAll('.toggle-option').forEach(button => {
            button.addEventListener('click', function() {
                const type = this.dataset.type;
                currentUserType = type;
                
                // Update active states
                document.querySelectorAll('.toggle-option').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Show/hide form sections and disable/enable inputs
                document.querySelectorAll('.form-section').forEach(section => {
                    section.classList.remove('active');
                    // Disable inputs in hidden sections
                    section.querySelectorAll('input, select').forEach(input => {
                        input.disabled = true;
                        input.removeAttribute('required');
                    });
                });
                
                const activeSection = document.getElementById(type + 'Form');
                activeSection.classList.add('active');
                // Enable inputs in active section
                activeSection.querySelectorAll('input, select').forEach(input => {
                    input.disabled = false;
                    input.setAttribute('required', 'required');
                });
                
                // Clear any existing error messages
                document.querySelectorAll('.error-message').forEach(msg => {
                    msg.classList.remove('show');
                    msg.textContent = '';
                });
                document.querySelectorAll('.form-input').forEach(input => {
                    input.classList.remove('error');
                });
            });
        });

        // Password validation
        function validatePassword(password, prefix = '') {
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password)
            };

            // Update requirement indicators
            document.getElementById(prefix + 'lengthReq').className = 
                'requirement-icon ' + (requirements.length ? 'valid' : 'invalid');
            document.getElementById(prefix + 'uppercaseReq').className = 
                'requirement-icon ' + (requirements.uppercase ? 'valid' : 'invalid');
            document.getElementById(prefix + 'lowercaseReq').className = 
                'requirement-icon ' + (requirements.lowercase ? 'valid' : 'invalid');
            document.getElementById(prefix + 'numberReq').className = 
                'requirement-icon ' + (requirements.number ? 'valid' : 'invalid');

            return Object.values(requirements).every(req => req);
        }

        // Real-time password validation
        document.getElementById('customerPassword').addEventListener('input', function() {
            validatePassword(this.value);
        });

        document.getElementById('companyPassword').addEventListener('input', function() {
            validatePassword(this.value, 'company');
        });

        // Simplified email validation
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Show error message
        function showError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            
            field.classList.add('error');
            errorElement.textContent = message;
            errorElement.classList.add('show');
        }

        // Clear error message
        function clearError(fieldId) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            
            field.classList.remove('error');
            errorElement.classList.remove('show');
            errorElement.textContent = '';
        }

        // Simplified real-time validation (optional)
        function setupRealTimeValidation() {
            // Minimal email validation only
            document.getElementById('customerEmail').addEventListener('blur', function() {
                const email = this.value.trim();
                if (email && !validateEmail(email)) {
                    showError('customerEmail', 'Please enter a valid email address');
                } else {
                    clearError('customerEmail');
                }
            });

            document.getElementById('companyEmail').addEventListener('blur', function() {
                const email = this.value.trim();
                if (email && !validateEmail(email)) {
                    showError('companyEmail', 'Please enter a valid email address');
                } else {
                    clearError('companyEmail');
                }
            });
        }

        // Initialize real-time validation
        setupRealTimeValidation();

        // Form submission that actually works with Django
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Re-enable all fields temporarily for form data collection
            document.querySelectorAll('.form-section input, .form-section select').forEach(input => {
                input.disabled = false;
            });
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Add user type to form data
            formData.append('user_type', currentUserType);
            
            // Submit to Django backend
            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('successMessage').classList.add('show');
                    this.reset();
                } else {
                    console.error('Registration failed:', data.errors);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });

        // Clear success message when switching user types
        document.querySelectorAll('.toggle-option').forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('successMessage').classList.remove('show');
            });
        });
    </script>
{% endblock %}
