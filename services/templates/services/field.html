{% extends 'main/base.html' %}

{% block title %}
    {{ field }} Services List
{% endblock %}

{% block content %}
    {% if services.all|length != 0 %}
        <div class="content">
            <p class="title">{{ field }} Services</p>
            <ul class="services_list">
                {% for service in services.all %}
                    <li style="margin-bottom: 30px;">
                        <div class="service_list_info">
                            <a href="/services/{{ service.id }}" style="font-weight: bold; font-size: 1.2em;">
                                {{ service.name }}
                            </a> — 
                            <span>{{ service.price_hour }}€/hour</span>
                            <pre>{{ service.description }}</pre>
                        </div>
                        <p style="text-align: right; font-size: small; margin: 5px 0;">
                            by 
                            <a href="/company/{{ service.company.user }}">
                                {{ service.company.user }}
                            </a>
                        </p>

                        {% if not forloop.last %}
                            <div class="line"></div>
                        {% endif %}
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% else %}
        <div class="content">
            <h2>Sorry. No {{ field }} services available.</h2>
        </div>
    {% endif %}
{% endblock %}
